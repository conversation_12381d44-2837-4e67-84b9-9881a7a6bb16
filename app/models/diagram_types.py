# diagram_types.py

from typing import Optional, List, Literal, Union
from pydantic import BaseModel


class BaseDiagramParams(BaseModel):
    diagramType: str


class VennSetItem(BaseModel):
    sets: List[str]
    size: int
    label: Optional[str] = None


class VennDiagramParams(BaseDiagramParams):
    diagramType: Literal["venn"]
    data: List[VennSetItem]


class Point(BaseModel):
    x: float
    y: float


class PolygonDiagramParams(BaseDiagramParams):
    diagramType: Literal["polygon"]
    width: float
    height: float
    points: List[Point]
    fill: Optional[str] = None
    stroke: Optional[str] = None
    strokeWidth: Optional[float] = None
    pointLabels: Optional[List[str]] = None
    lineLabels: Optional[List[str]] = None


class AngleLabel(BaseModel):
    label: Optional[str] = None
    angleDeg: Optional[float] = None
    angleRad: Optional[float] = None
    showDegreeSymbol: Optional[bool] = None
    color: Optional[str] = None
    fontSize: Optional[float] = None


class AngleDiagramParams(BaseDiagramParams):
    diagramType: Literal["angle"]
    fromAngleDeg: Optional[float] = None
    toAngleDeg: float
    draggable: Optional[Literal["from", "to", "both", "none"]] = None
    snapIncrement: Optional[float] = None
    drawClockwise: Optional[bool] = None
    arcColor: Optional[str] = None
    lineColor: Optional[str] = None
    labelColor: Optional[str] = None
    fontSize: Optional[float] = None
    labels: Optional[dict] = None
    angleLabel: Optional[str] = None
    highlightColor: Optional[str] = None
    showHandles: Optional[bool] = None


TransitionType = Literal[
    "fade",
    "zoom",
    "slide",
    "flip",
    "drop",
    "rotate",
    "warp",
    "diagonal",
    "distortFade",
    "ripple",
    "glitch",
    "explode",
    "orbit",
]


AnimationType = Literal[
    "infiniteRotate",
    "infiniteBounce",
    "infinitePulse",
    "infiniteWiggle",
    "infiniteGlow",
]


DiagramDefinition = Union[VennDiagramParams, PolygonDiagramParams, AngleDiagramParams]
