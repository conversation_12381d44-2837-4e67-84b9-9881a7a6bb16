from typing import Optional
from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from app.core.config import settings


# 1. Schema
class AngleStyleOutput(BaseModel):
    arcColor: Optional[str] = None
    lineColor: Optional[str] = None
    labelColor: Optional[str] = None
    highlightColor: Optional[str] = None
    fontSize: Optional[float] = None


# 2. Prompt
angle_style_prompt = PromptTemplate.from_template("""
You are extracting visual styling preferences for an angle diagram.

Return only valid JSON:
{{
  "arcColor"?: string,
  "lineColor"?: string,
  "labelColor"?: string,
  "highlightColor"?: string,
  "fontSize"?: number
}}

Guidelines:
- Colors may be specified by name ("red", "blue") or hex ("#ff0000").
- If the user says "use red for the arc", return arcColor: "red".
- If font size is mentioned ("use large text", "font size 16"), convert to a number.
- Do not return any values unless they are clearly specified.

Prompt: {prompt}
""")


# 3. Chain
llm = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0,
    api_key=settings.api_key,
).with_structured_output(AngleStyleOutput)

angle_style_chain = angle_style_prompt | llm
