from langchain_core.runnables import <PERSON>na<PERSON>Lambda, RunnableParallel
from app.chains.angle.extract_angle_intent import angle_intent_chain
from app.chains.angle.extract_angle_labels import angle_label_chain
from app.chains.angle.extract_angle_style import angle_style_chain
from app.models.diagram_types import AngleDiagramParams


async def _generate_angle_diagram_data(prompt: str) -> AngleDiagramParams:
    # Run the three chains in parallel
    parallel_results = RunnableParallel(
        intent=angle_intent_chain,
        labels=angle_label_chain,
        style=angle_style_chain
    ).invoke({"prompt": prompt})

    print("📐 Parallel Results:", parallel_results)

    # Assemble final diagram data
    diagram = AngleDiagramParams(
        diagramType="angle",
        toAngleDeg=parallel_results["intent"].toAngleDeg,
        fromAngleDeg=parallel_results["intent"].fromAngleDeg,
        drawClockwise=parallel_results["intent"].drawClockwise or None,
        draggable=parallel_results["intent"].draggable or None,
        snapIncrement=parallel_results["intent"].snapIncrement or None,
        labels=parallel_results["labels"].labels.model_dump() if parallel_results["labels"].labels else None,
        angleLabel=parallel_results["labels"].angleLabel or None,
        arcColor=parallel_results["style"].arcColor or None,
        lineColor=parallel_results["style"].lineColor or None,
        labelColor=parallel_results["style"].labelColor or None,
        highlightColor=parallel_results["style"].highlightColor or None,
        fontSize=parallel_results["style"].fontSize or None,
        showHandles=parallel_results["intent"].draggable != "none",
    )

    print("✅ Final AngleDiagramParams:", diagram)
    return diagram

async def generate_angle_diagram_data_func(input):
    return await _generate_angle_diagram_data(input)

generate_angle_diagram_data = RunnableLambda(generate_angle_diagram_data_func)