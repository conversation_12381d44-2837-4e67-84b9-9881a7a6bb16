from typing import Optional
from pydantic import BaseModel, Field
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from app.core.config import settings

class Labels(BaseModel):
    _from: Optional[str] = None
    _vertex: Optional[str] = None
    _to: Optional[str] = None


class AngleLabelOutput(BaseModel):
    angleLabel: Optional[str] = None
    labels: Optional[Labels] = None


# 2. Prompt
angle_label_prompt = PromptTemplate.from_template("""
You are extracting labeling information for an angle diagram.

Return JSON:
{{
  "angleLabel"?: string,
  "labels"?: {{
    "_from"?: string,
    "_vertex"?: string,
    "_to"?: string
  }}
}}

Guidelines:
- If the prompt includes an angle name like "∠ABC", "θ", or "30°", include it as angleLabel.
- If it names the three points that form the angle (like A, B, C), assign them to from, vertex, and to.
- Do not invent names — only extract them if clearly implied.
- If nothing is labeled, return an empty object.

Prompt: {prompt}
""")


# 3. Chain
llm = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0,
    api_key=settings.api_key,
).with_structured_output(AngleLabelOutput)

angle_label_chain = angle_label_prompt | llm
