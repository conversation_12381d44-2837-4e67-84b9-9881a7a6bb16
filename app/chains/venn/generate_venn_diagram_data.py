from langchain_core.runnables import Runnable<PERSON>ambda
from app.chains.venn.extract_num_sets import num_sets_chain
from app.chains.venn.extract_unique_sets import unique_sets_chain
from app.chains.venn.extract_overlaps import overlaps_chain
from app.models.diagram_types import VennDiagramParams


async def _generate_venn_diagram_data(prompt_data: dict) -> VennDiagramParams:
    initial_prompt = prompt_data["input"]

    # Step 1: Extract number of sets
    num_sets_object = await num_sets_chain.ainvoke(prompt_data)

    # Step 2: Extract unique sets
    unique_sets_object = await unique_sets_chain.ainvoke({
        "initialPrompt": initial_prompt,
        "numSets": num_sets_object.numSets,
    })

    # Default each unique set size to 10
    for entry in unique_sets_object.data:
        entry.size = 10

    # Step 3: Extract overlaps
    overlaps_object = await overlaps_chain.ainvoke({
        "sets": unique_sets_object,
        "initialPrompt": initial_prompt,
    })

    # Default each overlap size to 3
    for entry in overlaps_object.overlaps:
        entry.size = 3

    # Step 4: Combine and build final diagram
    final_data = unique_sets_object.data + overlaps_object.overlaps
    venn_diagram_data = VennDiagramParams(
        diagramType="venn",
        data=[ item.model_dump() for item in final_data],
    )

    return venn_diagram_data


async def generate_venn_diagram_data_func(input):
    return await _generate_venn_diagram_data(input)

generate_venn_diagram_data = RunnableLambda(generate_venn_diagram_data_func)