from typing import List
from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.runnables import <PERSON>nableLambda
from app.core.config import settings


# 1. Schema for overlap-only output
class Overlap(BaseModel):
    sets: List[str]
    size: int
    label: str


class VennOverlapsOutput(BaseModel):
    overlaps: List[Overlap]


# 2. Chat model
chat = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
)


# 3. Prompt template
venn_overlaps_prompt = PromptTemplate.from_template("""
You are generating overlaps (intersections) for a Venn diagram.

The user prompt was:
{initialPrompt}

The sets already defined in the diagram are:
{sets}

Your job is to find pairwise or multi-set intersections (if any are implied in the prompt) and return them as an array of overlap entries.

Return valid JSON in the format:
{{
  "overlaps": [
    {{"sets": [string, string], "size": number, "label": string}},
    ...
  ]
}}

Rules:
- If the prompt does not provide a number for the overlap, always set size to 3.
- Only include overlaps (2 or more sets in the "sets" array).
- Do NOT include individual sets.
- Do not invent overlaps. Only include overlaps if there is direct or strongly implied evidence in the prompt.
- You may include overlaps of 3 sets if clearly indicated in the prompt.
- Always use exact set names from the 'sets' list when filling the "sets" field.
- Labeling rules:
  - If the prompt contains specific counts (e.g., "15 people like both coffee and tea"), use the number as the label (e.g., "15").
  - Otherwise if counts are not available from the prompt, label using the names of overlapping sets joined with " and ", e.g., "coffee and tea".
- Return only valid JSON — no explanation, text, or markdown.
""")


# 4. Chain
overlaps_chain = (
    venn_overlaps_prompt
    | chat.with_structured_output(VennOverlapsOutput)
    | RunnableLambda(lambda output: _log_and_return_overlaps(output))
)


def _log_and_return_overlaps(output: VennOverlapsOutput) -> VennOverlapsOutput:
    import json
    print("🔄 [Venn Overlaps Output]:", json.dumps(output.dict(), indent=2))
    return output
