from langchain_core.runnables import RunnableLambda
from app.chains.polygon.extract_shape_intent import shape_intent_chain
from app.chains.polygon.extract_polygon_labels import polygon_labels_chain
from app.chains.polygon.polygon_diagram_builder import build_polygon_diagram
from app.models.diagram_types import PolygonDiagramParams


async def _generate_polygon_diagram_data(input: str) -> PolygonDiagramParams:
    user_prompt = input

    # Step 1: Extract shape intent
    print("🧠 Starting...")
    shape_intent = await shape_intent_chain.ainvoke({"input": user_prompt})
    print("🧠 Shape Intent:", shape_intent)

    # Step 2: If labeling needed, call label chain
    label_output = None
    if shape_intent.shouldLabelPoints or shape_intent.shouldLabelLines:
        label_output = await polygon_labels_chain.ainvoke({
            "numberOfSides": shape_intent.numberOfSides,
            "shouldLabelPoints": shape_intent.shouldLabelPoints,
            "shouldLabelLines": shape_intent.shouldLabelLines,
            "prompt": user_prompt,
        })
        print("🏷️ Label Output:", label_output)
    else:
        print("ℹ️ No labels requested.")

    # Step 3: Build the final diagram data
    diagram = await build_polygon_diagram(user_prompt, shape_intent, label_output)
    print("📐 Final Polygon Diagram:", diagram)

    return diagram


async def generate_polygon_diagram_data_func(input):
    return await _generate_polygon_diagram_data(input)

generate_polygon_diagram_data = RunnableLambda(generate_polygon_diagram_data_func)

