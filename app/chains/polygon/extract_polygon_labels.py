from typing import Optional, List
from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from app.core.config import settings


# 1. Schema
class PolygonLabelOutput(BaseModel):
    pointLabels: Optional[List[str]] = None
    lineLabels: Optional[List[str]] = None


# 2. Prompt
polygon_labels_prompt = PromptTemplate.from_template("""
Based on the user's prompt, generate labels for a polygon diagram.

You are given:
- numberOfSides = {numberOfSides}
- shouldLabelPoints = {shouldLabelPoints}
- shouldLabelLines = {shouldLabelLines}

If the prompt implies naming or labeling, return appropriate labels.

Return JSON:
{{
  "pointLabels": [...],  // optional
  "lineLabels": [...]    // optional
}}

Guidelines:
- If specific names like "3cm", "4cm", "5cm" are given, return those.
- Otherwise, generate ["A", "B", ...] for points.
- Do not exceed the numberOfSides.
- Return only what was implied/requested.
- If nothing is clearly requested, return nothing.

Prompt: {prompt}
""")


# 3. Chain
llm = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
).with_structured_output(PolygonLabelOutput)

polygon_labels_chain = polygon_labels_prompt | llm
