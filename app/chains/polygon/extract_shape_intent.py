from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from app.core.config import settings


# 1. Schema
class ShapeIntent(BaseModel):
    numberOfSides: int
    shouldLabelPoints: bool
    shouldLabelLines: bool


# 2. Prompt
shape_intent_prompt = PromptTemplate.from_template("""
You are extracting polygon diagram intent from the user prompt.

Return JSON:
{{
  "numberOfSides": number,
  "shouldLabelPoints": boolean,
  "shouldLabelLines": boolean
}}

Guidelines:
- Only include labeling booleans if the user clearly mentions labeling.
- Recognize polygon names: triangle = 3, pentagon = 5, hexagon = 6, etc.
- Do not include label names. That will be handled elsewhere.

Prompt: {input}
""")


# 3. Chain
llm = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
).with_structured_output(ShapeIntent)

shape_intent_chain = shape_intent_prompt | llm
