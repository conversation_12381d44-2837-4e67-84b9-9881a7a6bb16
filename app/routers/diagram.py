from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from app.services.diagram_generator import get_diagram_definition_from_prompt
from typing import Union
from app.models.diagram_types import VennDiagramParams, PolygonDiagramParams, AngleDiagramParams

router = APIRouter(
    prefix="/diagram",
    tags=["diagram"],
)

# Request body schema
class PromptRequest(BaseModel):
    prompt: str


# Response schema (loosely typed since it could be any of the diagram types)
class DiagramResponse(BaseModel):
    diagram: Union[
    VennDiagramParams, PolygonDiagramParams, AngleDiagramParams, dict
]


@router.post("/generate", response_model=DiagramResponse)
async def generate_diagram(request: PromptRequest):
    """
    Generates diagram data based on the user's natural language prompt.
    """
    try:
        diagram = await get_diagram_definition_from_prompt(request.prompt)
        return {"diagram": diagram}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
