# Diagram Types Documentation

This document provides comprehensive information about the diagram types supported by the API and their parameter shapes in JSON format.

## Overview

The API supports the following diagram types:
- Venn Diagrams
- Polygon Diagrams
- Angle Diagrams

Each diagram type has a specific JSON structure that must be followed when making requests or processing responses.

## Common Structure

All diagram types share a common base structure:

```json
{
  "diagramType": "string"
}
```

Where `diagramType` is one of: `"venn"`, `"polygon"`, or `"angle"`.

## Venn Diagram

Venn diagrams represent relationships between sets.

```json
{
  "diagramType": "venn",
  "data": [
    {
      "sets": ["string"],
      "size": number,
      "label": "string"
    },
    ...
  ]
}
```

### Parameters

- `data`: Array of set items
  - `sets`: Array of set names. For individual sets, this contains a single string. For intersections, it contains multiple strings.
  - `size`: Numeric value representing the size of the set or intersection
  - `label`: Optional text label for the set or intersection

### Example

```json
{
  "diagramType": "venn",
  "data": [
    {
      "sets": ["Coffee"],
      "size": 12,
      "label": "Coffee(12)"
    },
    {
      "sets": ["Tea"],
      "size": 10,
      "label": "Tea(10)"
    },
    {
      "sets": ["Coffee", "Tea"],
      "size": 5,
      "label": "5"
    }
  ]
}
```

This example represents a Venn diagram showing 12 people who like coffee, 10 people who like tea, and 5 people who like both.

## Polygon Diagram

Polygon diagrams represent geometric shapes with vertices and edges.

```json
{
  "diagramType": "polygon",
  "width": number,
  "height": number,
  "points": [
    {
      "x": number,
      "y": number
    },
    ...
  ],
  "fill": "string",
  "stroke": "string",
  "strokeWidth": number,
  "pointLabels": ["string", ...],
  "lineLabels": ["string", ...]
}
```

### Parameters

- `width`: Width of the diagram canvas
- `height`: Height of the diagram canvas
- `points`: Array of point coordinates
  - `x`: X-coordinate (0-1 range)
  - `y`: Y-coordinate (0-1 range)
- `fill`: Optional fill color (CSS color string)
- `stroke`: Optional stroke color (CSS color string)
- `strokeWidth`: Optional stroke width
- `pointLabels`: Optional array of labels for vertices
- `lineLabels`: Optional array of labels for edges

### Example

```json
{
  "diagramType": "polygon",
  "width": 1,
  "height": 1,
  "points": [
    { "x": 0.5, "y": 0.1 },
    { "x": 0.9, "y": 0.5 },
    { "x": 0.5, "y": 0.9 },
    { "x": 0.1, "y": 0.5 }
  ],
  "fill": "#f5f5f5",
  "stroke": "black",
  "strokeWidth": 1,
  "pointLabels": ["A", "B", "C", "D"],
  "lineLabels": ["AB", "BC", "CD", "DA"]
}
```

This example represents a square with labeled vertices (A, B, C, D) and labeled edges (AB, BC, CD, DA).

## Angle Diagram

Angle diagrams represent geometric angles with various customization options.

```json
{
  "diagramType": "angle",
  "toAngleDeg": number,
  "fromAngleDeg": number,
  "draggable": "from" | "to" | "both" | "none",
  "snapIncrement": number,
  "drawClockwise": boolean,
  "arcColor": "string",
  "lineColor": "string",
  "labelColor": "string",
  "fontSize": number,
  "labels": {
    "_from": "string",
    "_vertex": "string",
    "_to": "string"
  },
  "angleLabel": "string",
  "highlightColor": "string",
  "showHandles": boolean
}
```

### Parameters

- `toAngleDeg`: End angle in degrees
- `fromAngleDeg`: Start angle in degrees (default: 0)
- `draggable`: Which parts of the angle can be adjusted
- `snapIncrement`: Angle snapping increment in degrees
- `drawClockwise`: Whether to draw the angle clockwise
- `arcColor`: Color of the angle arc
- `lineColor`: Color of the angle lines
- `labelColor`: Color of the labels
- `fontSize`: Size of the label text
- `labels`: Object containing labels for different parts of the angle
  - `_from`: Label for the starting ray
  - `_vertex`: Label for the vertex
  - `_to`: Label for the ending ray
- `angleLabel`: Label for the angle itself
- `highlightColor`: Color used for highlighting
- `showHandles`: Whether to display adjustment handles

### Example

```json
{
  "diagramType": "angle",
  "toAngleDeg": 45,
  "fromAngleDeg": 0,
  "draggable": "to",
  "snapIncrement": 15,
  "drawClockwise": true,
  "arcColor": "blue",
  "lineColor": "black",
  "labelColor": "darkblue",
  "fontSize": 14,
  "labels": {
    "_from": "A",
    "_vertex": "O",
    "_to": "B"
  },
  "angleLabel": "45°",
  "highlightColor": "rgba(0, 0, 255, 0.2)",
  "showHandles": true
}
```

This example represents a 45-degree angle with labeled points (A, O, B), where the end ray is draggable and snaps to 15-degree increments.

## Transitions and Animations

The API also supports transition and animation effects for diagrams.

### Transition Types

```
"fade" | "zoom" | "slide" | "flip" | "drop" | "rotate" | "warp" | "diagonal" | "distortFade" | "ripple" | "glitch" | "explode" | "orbit"
```

### Animation Types

```
"infiniteRotate" | "infiniteBounce" | "infinitePulse" | "infiniteWiggle" | "infiniteGlow"
```

### Example with Transitions

```json
{
  "diagramType": "polygon",
  "width": 1,
  "height": 1,
  "points": [
    { "x": 0.5, "y": 0.1 },
    { "x": 0.9, "y": 0.5 },
    { "x": 0.5, "y": 0.9 },
    { "x": 0.1, "y": 0.5 }
  ],
  "fill": "#f5f5f5",
  "stroke": "black",
  "strokeWidth": 1,
  "transition": "fade",
  "animation": "infiniteRotate"
}
```

This example shows a square that fades in and continuously rotates.
